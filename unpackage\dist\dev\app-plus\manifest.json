{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__DCBAD98", "name": "优乐G", "version": {"name": "5.2.1", "code": 521}, "description": "优乐G，让优惠触手可及，让您的生活更加精彩！", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Payment": {}, "Share": {}, "VideoPlayer": {}, "OAuth": {}, "Barcode": {}, "Camera": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"autoclose": false, "waiting": false, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#fff"}, "usingComponents": true, "nvueCompiler": "uni-app", "safearea": {"bottom": {"offset": "none"}}, "distribute": {"splashscreen": {"androidStyle": "common", "iosStyle": "common", "useOriginalMsgbox": true}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}, "google": {"permissions": ["<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.CAPTURE_AUDIO_OUTPUT\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a"], "permissionExternalStorage": {"request": "none", "prompt": "应用保存运行状态等信息，需要获取读写手机存储（系统提示为访问设备上的照片、媒体内容和文件）权限，请允许。"}, "permissionPhoneState": {"request": "none"}}, "apple": {"capabilities": {"entitlements": {"com.apple.developer.associated-domains": ["applinks:", "applinks:", "applinks:", "applinks:static-679f0930-8f60-425c-9033-8c135f397ea5.bspapp.com"]}}, "privacyDescription": {"NSLocationWhenInUseUsageDescription": "根据客户地理位置推荐最近门店", "NSPhotoLibraryUsageDescription": "上传用户头像保存分享海报", "NSPhotoLibraryAddUsageDescription": "上传用户头像保存分享海报", "NSLocationAlwaysAndWhenInUseUsageDescription": "根据客户地理位置推荐最近门店", "NSLocationAlwaysUsageDescription": "根据客户地理位置推荐最近门店", "NSCameraUsageDescription": "上传用户头像保存分享海报"}, "idfa": false, "dSYMs": false}, "plugins": {"payment": {"alipay": {"__platform__": ["ios", "android"]}, "weixin": {"__platform__": ["ios", "android"], "appid": "wx277a269f3d736d67", "UniversalLinks": "https://bzapp.crmeb.net/uni-universallinks/__UNI__A3F1ED4/"}}, "share": {"weixin": {"appid": "wx277a269f3d736d67", "UniversalLinks": "https://bzapp.crmeb.net/uni-universallinks/__UNI__A3F1ED4/"}}, "push": {}, "maps": {"amap": {"appkey_ios": "aeb768547b9d752891e37e1ca0a2b66d", "appkey_android": "41ec5c3f4d110ce02a326210fe147be8"}}, "oauth": {"apple": {}, "weixin": {"appid": "wx277a269f3d736d67", "appsecret": "bd08741a055c2ecac5826ff1c048464b", "UniversalLinks": "https://bzapp.crmeb.net/uni-universallinks/__UNI__A3F1ED4/"}}, "ad": {}, "geolocation": {"amap": {"__platform__": ["ios", "android"], "appkey_ios": "aeb768547b9d752891e37e1ca0a2b66d", "appkey_android": "41ec5c3f4d110ce02a326210fe147be8"}}, "statics": {"google": {"config_ios": "", "config_android": ""}}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "arguments": "{\"name\":\"\",\"query\":\"\",\"id\":0,\"pathName\":\"pages/index/index\"}", "allowsInlineMediaPlayback": true, "uni-app": {"compilerVersion": "4.66", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "tabBar": {"color": "#282828", "selectedColor": "#ff3366", "borderStyle": "rgba(255,255,255,0.4)", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/index/index", "iconPath": "static/images/1-001.png", "selectedIconPath": "static/images/1-002.png", "text": "首页"}, {"pagePath": "pages/goods_cate/goods_cate", "iconPath": "static/images/2-001.png", "selectedIconPath": "static/images/2-002.png", "text": "分类"}, {"pagePath": "pages/order_addcart/order_addcart", "iconPath": "static/images/3-001.png", "selectedIconPath": "static/images/3-002.png", "text": "购物车"}, {"pagePath": "pages/user/index", "iconPath": "static/images/4-001.png", "selectedIconPath": "static/images/4-002.png", "text": "我的"}], "height": "50px", "child": ["lauchwebview"], "selected": 0}, "launch_path": "__uniappview.html"}, "screenOrientation": ["portrait-primary", "portrait-secondary", "landscape-primary", "landscape-secondary"]}