<template>
	<view class="merchant-apply-container" :style="colorStyle">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight }">
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<text class="iconfont icon-fanhui"></text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">商户入驻申请</text>
				</view>
				<view class="navbar-right" @click="goToRecord">
					<text class="record-text">申请记录</text>
					<text class="iconfont icon-xiangyou"></text>
				</view>
			</view>
		</view>

		<!-- 主要内容 -->
		<view class="main-content" v-if="status == -1 && !loading">
			<!-- 头部背景和标题 -->
			<view class="header-section">
				<view class="header-bg">
					<view class="header-content">
						<view class="title-section">
							<text class="main-title">商户入驻申请</text>
							<text class="sub-title">合作共赢 共享市场</text>
						</view>
						
					</view>
				</view>
			</view>

			<!-- 表单区域 -->
			<view class="form-container">
				<view class="form-card">
					<!-- 商户名称 -->
					<view class="form-item">
						<text class="item-label">商户名称</text>
						<input type="text" :placeholder="$t('请输入商户名称')" v-model="formData.merchant_name"
							@input="validateForm" placeholder-class="placeholder-style" />
					</view>

					<!-- 用户姓名 -->
					<view class="form-item">
						<text class="item-label">用户姓名</text>
						<input type="text" :placeholder="$t('请输入姓名')" v-model="formData.user_name" @input="validateForm"
							placeholder-class="placeholder-style" />
					</view>

					<!-- 联系电话 -->
					<view class="form-item">
						<text class="item-label">联系电话</text>
						<input type="text" :placeholder="$t('请输入手机号')" v-model="formData.phone" @input="validateForm"
							placeholder-class="placeholder-style" />
					</view>

					<!-- 验证码 -->
					<view class="form-item verify-item">
						<text class="item-label">验证码</text>
						<input type="text" :placeholder="$t('填写验证码')" v-model="formData.verify_code"
							@input="validateForm" placeholder-class="placeholder-style" class="verify-input" />
						<button class="verify-btn" :class="{ 'disabled': codeDisabled }" :disabled="codeDisabled"
							@click="sendVerifyCode">
							{{ codeText }}
						</button>
					</view>

					<!-- 商户分类 -->
					<picker
						:value="categoryIndex"
						:range="categoryList"
						range-key="name"
						@change="selectCategory"
					>
						<view class="form-item picker-item">
							<text class="item-label">商户分类</text>
							<text class="picker-text" :class="{ 'placeholder-style': !formData.category_name }">
								{{ formData.category_name || $t('请选择商户分类') }}
							</text>
							<text class="iconfont icon-xiangyou picker-arrow"></text>
						</view>
					</picker>

					<!-- 店铺类型 -->
					<picker
						:value="storeTypeIndex"
						:range="storeTypeList"
						range-key="name"
						@change="selectStoreType"
					>
						<view class="form-item picker-item">
							<text class="item-label">店铺类型</text>
							<text class="picker-text" :class="{ 'placeholder-style': !formData.store_type_name }">
								{{ formData.store_type_name || $t('请选择店铺类型') }}
							</text>
							<text class="iconfont icon-xiangyou picker-arrow"></text>
						</view>
					</picker>

					<!-- 营业执照上传 -->
					<view class="form-item upload-item">
						<view class="upload-title">
							<text class="upload-label">请上传营业执照及行业相关资质证明图片</text>
							<text class="upload-desc">(图片最多可上传10张,图片格式支持JPG、PNG、JPEG)</text>
						</view>
						<view class="upload-container">
							<view class="image-item" v-for="(item, index) in images" :key="index"
								@click="previewImage(index)">
								<image :src="item" mode="aspectFill"></image>
								<text class="iconfont icon-guanbi1 delete-btn" @click.stop="deleteImage(index)"></text>
							</view>
							<view class="upload-btn" v-if="images.length < 10" @click="uploadImage">
								<text class="iconfont icon-icon25201 upload-icon"></text>
								<text class="upload-text">上传图片</text>
							</view>
						</view>
					</view>

					<!-- 协议勾选 -->
					<view class="form-item agreement-item">
						<checkbox-group @change="changeAgreement">
							<checkbox class="agreement-checkbox" :checked="isAgree" />
						</checkbox-group>
						<text class="agreement-text">已阅读并同意</text>
						<text class="agreement-link" @click="showAgreement">《入驻协议》</text>
					</view>

					<!-- 提交按钮 -->
					<button class="submit-btn" :class="{ 'active': canSubmit }" :disabled="!canSubmit"
						@click="submitForm">
						提交申请
					</button>
				</view>
			</view>
		</view>

		<!-- 成功状态 -->
		<view class="success-container" v-else-if="status == 0 || status == 1">
			<view class="success-content">
				<image class="success-icon" src="../static/success.png"></image>
				<text class="success-title" v-if="status == 0">恭喜，您的资料提交成功！</text>
				<text class="success-title" v-else>恭喜，您的资料通过审核！</text>
				<button class="home-btn" @click="goHome">返回首页</button>
			</view>
		</view>

		<!-- 失败状态 -->
		<view class="fail-container" v-else-if="status == 2">
			<view class="fail-content">
				<image class="fail-icon" src="../static/error.png"></image>
				<text class="fail-title">您的申请未通过！</text>
				<text class="fail-reason" v-if="refusal_reason">{{ refusal_reason }}</text>
				<button class="retry-btn" @click="retryApply">重新申请</button>
				<button class="home-btn" @click="goHome">返回首页</button>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<text class="loading-icon iconfont icon-jiazai"></text>
		</view>



		<!-- 协议弹窗 -->
		<view class="agreement-modal" v-if="showAgreementModal" @click="hideAgreement">
			<view class="agreement-content" @click.stop>
				<view class="agreement-header">
					<text class="agreement-title">商户入驻协议</text>
					<text class="iconfont icon-cha close-btn" @click="hideAgreement"></text>
				</view>
				<scroll-view class="agreement-body" scroll-y>
					<jyf-parser :html="agreementContent" ref="article" :tag-style="tagStyle"></jyf-parser>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		merchantApply,
		getMerchantApplyInfo,
		getMerchantCategories,
		getStoreTypes,
		getMerchantAgreement
	} from '@/api/merchant.js';
	import {
		getCodeApi,
		registerVerify
	} from '@/api/user.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import parser from "@/components/jyf-parser/jyf-parser";
	import colors from "@/mixins/color";
	import sendVerifyCode from "@/mixins/SendVerifyCode";

	export default {
		components: {
			"jyf-parser": parser
		},
		mixins: [colors, sendVerifyCode],
		data() {
			return {
				statusBarHeight: uni.getSystemInfoSync().statusBarHeight + 'px',
				loading: false,
				status: -1, // -1: 申请中, 0: 已提交, 1: 已通过, 2: 已拒绝
				formData: {
					merchant_name: '',
					user_name: '',
					phone: '',
					verify_code: '',
					category_id: '',
					category_name: '',
					store_type_id: '',
					store_type_name: ''
				},
				images: [],
				isAgree: false,
				canSubmit: false,

				// 验证码相关
				codeDisabled: false,
				codeText: '获取验证码',
				keyCode: '',

				// 选择器相关
				categoryList: [],
				storeTypeList: [],
				categoryIndex: 0,
				storeTypeIndex: 0,

				// 协议相关
				showAgreementModal: false,
				agreementContent: '',

				// 其他
				refusal_reason: '',
				tagStyle: {
					img: 'width:100%;display:block;',
					table: 'width:100%',
					video: 'width:100%'
				}
			};
		},
		computed: {
			...mapGetters(['isLogin'])
		},
		onLoad() {
			if (this.isLogin) {
				this.initData();
			} else {
				toLogin();
			}
		},
		methods: {
			// 初始化数据
			async initData() {
				this.loading = true;
				try {
					await Promise.all([
						this.getMerchantApplyInfo(),
						this.loadCategories(),
						this.loadStoreTypes()
					]);
				} catch (error) {
					console.error('初始化数据失败:', error);
				} finally {
					this.loading = false;
				}
			},

			// 获取申请信息
			async getMerchantApplyInfo() {
				try {
					const res = await getMerchantApplyInfo();
					if (res.data.status !== -1) {
						this.status = res.data.status;
						Object.keys(this.formData).forEach(key => {
							if (res.data[key] !== undefined) {
								this.formData[key] = res.data[key];
							}
						});
						if (res.data.images) {
							this.images = res.data.images;
						}
						if (res.data.refusal_reason) {
							this.refusal_reason = res.data.refusal_reason;
						}
					}
				} catch (error) {
					console.error('获取申请信息失败:', error);
				}
			},

			// 加载商户分类
			async loadCategories() {
				try {
					const res = await getMerchantCategories();
					this.categoryList = res.data.list || [];
				} catch (error) {
					console.error('加载商户分类失败:', error);
					// 添加模拟数据用于测试
					this.categoryList = [
						{ id: 1, name: '餐饮美食' },
						{ id: 2, name: '服装鞋帽' },
						{ id: 3, name: '数码电器' },
						{ id: 4, name: '美容护理' },
						{ id: 5, name: '生活服务' }
					];
				}
			},

			// 加载店铺类型
			async loadStoreTypes() {
				try {
					const res = await getStoreTypes();
					this.storeTypeList = res.data.list || [];
				} catch (error) {
					console.error('加载店铺类型失败:', error);
					// 添加模拟数据用于测试
					this.storeTypeList = [
						{ id: 1, name: '实体店铺' },
						{ id: 2, name: '线上店铺' },
						{ id: 3, name: '连锁店铺' },
						{ id: 4, name: '个人工作室' }
					];
				}
			},

			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 跳转到申请记录
			goToRecord() {
				uni.navigateTo({
					url: '/pages/annex/merchant_record/index'
				});
			},

			// 返回首页
			goHome() {
				uni.switchTab({
					url: '/pages/index/index'
				});
			},

			// 重新申请
			retryApply() {
				this.status = -1;
				this.refusal_reason = '';
			},

			// 表单验证
			validateForm() {
				const {
					merchant_name,
					user_name,
					phone,
					verify_code,
					category_id,
					store_type_id
				} = this.formData;
				this.canSubmit = merchant_name && user_name && phone && verify_code &&
					category_id && store_type_id && this.images.length > 0 && this.isAgree;
			},

			// 发送验证码
			async sendVerifyCode() {
				if (!this.formData.phone) {
					return this.$util.Tips({
						title: '请输入手机号'
					});
				}
				if (!/^1[3-9]\d{9}$/.test(this.formData.phone)) {
					return this.$util.Tips({
						title: '请输入正确的手机号码'
					});
				}

				try {
					this.codeDisabled = true;
					const keyRes = await getCodeApi();
					this.keyCode = keyRes.data.key;

					await registerVerify({
						phone: this.formData.phone,
						key: this.keyCode,
						type: 'merchant'
					});

					this.$util.Tips({
						title: '验证码发送成功'
					});
					this.startCountdown();
				} catch (error) {
					this.codeDisabled = false;
					this.$util.Tips({
						title: error.message || '发送失败'
					});
				}
			},

			// 开始倒计时
			startCountdown() {
				let count = 60;
				this.codeText = `${count}s`;
				const timer = setInterval(() => {
					count--;
					if (count <= 0) {
						clearInterval(timer);
						this.codeDisabled = false;
						this.codeText = '重新获取';
					} else {
						this.codeText = `${count}s`;
					}
				}, 1000);
			},



			// 选择分类
			selectCategory(e) {
				const index = e.detail.value;
				this.categoryIndex = index;
				this.formData.category_id = this.categoryList[index].id;
				this.formData.category_name = this.categoryList[index].name;
				this.validateForm();
			},

			// 选择店铺类型
			selectStoreType(e) {
				const index = e.detail.value;
				this.storeTypeIndex = index;
				this.formData.store_type_id = this.storeTypeList[index].id;
				this.formData.store_type_name = this.storeTypeList[index].name;
				this.validateForm();
			},

			// 上传图片
			uploadImage() {
				const that = this;
				uni.chooseImage({
					count: 10 - this.images.length,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						res.tempFilePaths.forEach(tempPath => {
							that.$util.uploadImageOne('upload/image', (uploadRes) => {
								that.images.push(uploadRes.data.url);
								that.validateForm();
							}, tempPath);
						});
					}
				});
			},

			// 删除图片
			deleteImage(index) {
				this.images.splice(index, 1);
				this.validateForm();
			},

			// 预览图片
			previewImage(index) {
				uni.previewImage({
					current: this.images[index],
					urls: this.images
				});
			},

			// 协议勾选
			changeAgreement() {
				this.isAgree = !this.isAgree;
				this.validateForm();
			},

			// 显示协议
			async showAgreement() {
				try {
					const res = await getMerchantAgreement();
					this.agreementContent = res.data.content || '';
					this.showAgreementModal = true;
				} catch (error) {
					this.$util.Tips({
						title: '获取协议失败'
					});
				}
			},

			// 隐藏协议
			hideAgreement() {
				this.showAgreementModal = false;
			},

			// 提交表单
			async submitForm() {
				if (!this.canSubmit) return;

				try {
					this.loading = true;
					const submitData = {
						...this.formData,
						images: this.images
					};

					const res = await merchantApply(submitData);
					if (res.status === 200) {
						this.$util.Tips({
							title: '提交成功'
						});
						setTimeout(() => {
							this.getMerchantApplyInfo();
						}, 1000);
					}
				} catch (error) {
					this.$util.Tips({
						title: error.message || '提交失败'
					});
				} finally {
					this.loading = false;
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.merchant-apply-container {
		min-height: 100vh;
		background: linear-gradient(180deg, #FF6B35 0%, #FF8A65 100%);
	}

	/* 自定义导航栏 */
	.custom-navbar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: transparent;

		.navbar-content {
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 32rpx;

			.navbar-left {
				width: 80rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.iconfont {
					font-size: 36rpx;
					color: #FFFFFF;
				}
			}

			.navbar-center {
				flex: 1;
				text-align: center;

				.navbar-title {
					font-size: 36rpx;
					font-weight: 600;
					color: #FFFFFF;
				}
			}

			.navbar-right {
				display: flex;
				align-items: center;

				.record-text {
					font-size: 28rpx;
					color: #FFFFFF;
					margin-right: 8rpx;
				}

				.iconfont {
					font-size: 24rpx;
					color: #FFFFFF;
				}
			}
		}
	}

	/* 主要内容 */
	.main-content {
		padding-top: 88rpx;
	}

	/* 头部区域 */
	.header-section {
		padding: 40rpx 32rpx 60rpx;

		.header-content {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.title-section {
				.main-title {
					font-size: 48rpx;
					font-weight: bold;
					color: #FFFFFF;
					display: block;
					margin-bottom: 16rpx;
				}

				.sub-title {
					font-size: 28rpx;
					color: #FFFFFF;
					opacity: 0.9;
				}
			}

			.join-btn-container {
				.join-btn {
					width: 160rpx;
					height: 64rpx;
					background: #FFFFFF;
					border-radius: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					.join-text {
						font-size: 28rpx;
						color: #FF6B35;
						font-weight: 600;
					}
				}
			}
		}
	}

	/* 表单容器 */
	.form-container {
		padding: 0 32rpx;

		.form-card {
			background: #FFFFFF;
			border-radius: 24rpx;
			padding: 40rpx 32rpx;
			margin-bottom: 40rpx;
		}
	}

	/* 表单项 */
	.form-item {
		margin-bottom: 40rpx;

		&:last-child {
			margin-bottom: 0;
		}

		.item-label {
			font-size: 32rpx;
			color: #333333;
			font-weight: 600;
			display: block;
			margin-bottom: 20rpx;
		}

		input {
			width: 100%;
			height: 88rpx;
			background: #F8F9FA;
			border-radius: 12rpx;
			padding: 0 24rpx;
			font-size: 30rpx;
			color: #333333;
			border: none;

			&.verify-input {
				width: calc(100% - 200rpx);
			}
		}

		.placeholder-style {
			color: #CCCCCC;
		}
	}

	/* 验证码项 */
	.verify-item {
		display: flex;
		align-items: flex-end;
		flex-wrap: wrap;

		.item-label {
			width: 100%;
			margin-bottom: 20rpx;
		}

		.verify-btn {
			width: 180rpx;
			height: 88rpx;
			background: #FF6B35;
			border-radius: 12rpx;
			border: none;
			font-size: 28rpx;
			color: #FFFFFF;
			margin-left: 20rpx;
			line-height: 88rpx;

			&.disabled {
				background: #CCCCCC;
			}
		}
	}

	/* 选择器项 */
	.picker-item {
		display: flex;
		align-items: center;
		background: #F8F9FA;
		border-radius: 12rpx;
		padding: 24rpx;
		min-height: 88rpx;

		.item-label {
			font-size: 32rpx;
			color: #333333;
			font-weight: 600;
			margin-bottom: 0;
			margin-right: 24rpx;
			min-width: 160rpx;
		}

		.picker-text {
			flex: 1;
			font-size: 30rpx;
			color: #333333;

			&.placeholder-style {
				color: #CCCCCC;
			}
		}

		.picker-arrow {
			font-size: 24rpx;
			color: #CCCCCC;
		}
	}

	/* 上传项 */
	.upload-item {
		.upload-title {
			margin-bottom: 24rpx;

			.upload-label {
				font-size: 32rpx;
				color: #333333;
				font-weight: 600;
				display: block;
				margin-bottom: 8rpx;
			}

			.upload-desc {
				font-size: 24rpx;
				color: #999999;
			}
		}

		.upload-container {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;

			.image-item {
				position: relative;
				width: 160rpx;
				height: 160rpx;
				border-radius: 12rpx;
				overflow: hidden;

				image {
					width: 100%;
					height: 100%;
				}

				.delete-btn {
					position: absolute;
					top: 8rpx;
					right: 8rpx;
					width: 32rpx;
					height: 32rpx;
					background: rgba(0, 0, 0, 0.6);
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 20rpx;
					color: #FFFFFF;
				}
			}

			.upload-btn {
				width: 160rpx;
				height: 160rpx;
				background: #F8F9FA;
				border: 2rpx dashed #CCCCCC;
				border-radius: 12rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.upload-icon {
					font-size: 48rpx;
					color: #CCCCCC;
					margin-bottom: 8rpx;
				}

				.upload-text {
					font-size: 24rpx;
					color: #CCCCCC;
				}
			}
		}
	}

	/* 协议项 */
	.agreement-item {
		display: flex;
		align-items: center;
		margin-bottom: 60rpx !important;

		.agreement-checkbox {
			margin-right: 16rpx;
		}

		.agreement-text {
			font-size: 28rpx;
			color: #666666;
			margin-right: 8rpx;
		}

		.agreement-link {
			font-size: 28rpx;
			color: #FF6B35;
			text-decoration: underline;
		}
	}

	/* 提交按钮 */
	.submit-btn {
		width: 100%;
		height: 88rpx;
		background: #CCCCCC;
		border-radius: 44rpx;
		border: none;
		font-size: 32rpx;
		color: #FFFFFF;
		font-weight: 600;

		&.active {
			background: #FF6B35;
		}
	}

	/* 成功状态 */
	.success-container {
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 100vh;
		padding: 40rpx;

		.success-content {
			text-align: center;

			.success-icon {
				width: 200rpx;
				height: 200rpx;
				margin-bottom: 40rpx;
			}

			.success-title {
				font-size: 36rpx;
				color: #FFFFFF;
				font-weight: 600;
				margin-bottom: 60rpx;
				display: block;
			}

			.home-btn {
				width: 300rpx;
				height: 80rpx;
				background: #FFFFFF;
				border-radius: 40rpx;
				border: none;
				font-size: 30rpx;
				color: #FF6B35;
				font-weight: 600;
			}
		}
	}

	/* 失败状态 */
	.fail-container {
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 100vh;
		padding: 40rpx;

		.fail-content {
			text-align: center;

			.fail-icon {
				width: 200rpx;
				height: 200rpx;
				margin-bottom: 40rpx;
			}

			.fail-title {
				font-size: 36rpx;
				color: #FFFFFF;
				font-weight: 600;
				margin-bottom: 20rpx;
				display: block;
			}

			.fail-reason {
				font-size: 28rpx;
				color: #FFFFFF;
				opacity: 0.8;
				margin-bottom: 60rpx;
				display: block;
			}

			.retry-btn {
				width: 300rpx;
				height: 80rpx;
				background: #FFFFFF;
				border-radius: 40rpx;
				border: none;
				font-size: 30rpx;
				color: #FF6B35;
				font-weight: 600;
				margin-bottom: 20rpx;
			}

			.home-btn {
				width: 300rpx;
				height: 80rpx;
				background: transparent;
				border: 2rpx solid #FFFFFF;
				border-radius: 40rpx;
				font-size: 30rpx;
				color: #FFFFFF;
				font-weight: 600;
			}
		}
	}

	/* 加载状态 */
	.loading-container {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;

		.loading-icon {
			font-size: 60rpx;
			color: #FFFFFF;
			animation: rotate 1s linear infinite;
		}
	}

	/* 协议弹窗 */
	.agreement-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		padding: 40rpx;

		.agreement-content {
			width: 100%;
			max-width: 600rpx;
			max-height: 80vh;
			background: #FFFFFF;
			border-radius: 24rpx;
			overflow: hidden;

			.agreement-header {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 32rpx;
				border-bottom: 1rpx solid #F0F0F0;

				.agreement-title {
					font-size: 32rpx;
					color: #333333;
					font-weight: 600;
				}

				.close-btn {
					font-size: 32rpx;
					color: #999999;
				}
			}

			.agreement-body {
				max-height: 60vh;
				padding: 32rpx;
			}
		}
	}

	/* 动画 */
	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}

	/* 响应式适配 */
	@media screen and (max-width: 750rpx) {
		.header-content {
			flex-direction: column;
			align-items: flex-start !important;

			.title-section {
				margin-bottom: 24rpx;
			}
		}

		.upload-container {

			.image-item,
			.upload-btn {
				width: 140rpx !important;
				height: 140rpx !important;
			}
		}
	}
</style>